[{"id": "d3caf6daf68cc456", "type": "tab", "label": "水泵气泵轮换控制系统", "disabled": false, "info": "实现浮球激活时水泵轮换启动，气泵故障切换和连续运行的控制逻辑", "env": []}, {"id": "be585df13660cc03", "type": "ui_group", "name": "<PERSON><PERSON><PERSON>", "tab": "", "disp": true, "width": "6", "collapse": false, "className": ""}, {"id": "timer_inject_node", "type": "inject", "z": "d3caf6daf68cc456", "name": "定时检查浮球状态", "props": [{"p": "payload"}], "repeat": "5", "crontab": "", "once": true, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 160, "y": 100, "wires": [["get_float1_node"]]}, {"id": "get_float1_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "获取浮球状态(DI01)", "nodeid": "DI01", "datatype": "INT", "x": 360, "y": 100, "wires": [["check_auto_mode_function"]]}, {"id": "check_auto_mode_function", "type": "function", "z": "d3caf6daf68cc456", "name": "检查自动档位", "func": "// 获取浮球状态\nvar floatStatus = parseInt(msg.payload) || 0;\n\n// 获取各设备的档位状态\n// DI15-DI18代表自动档位：水泵1、水泵2、气泵1、气泵2\nvar waterPump1Auto = global.get('DI15') || 0;  // 水泵1自动档位\nvar waterPump2Auto = global.get('DI16') || 0;  // 水泵2自动档位\nvar airPump1Auto = global.get('DI17') || 0;    // 气泵1自动档位\nvar airPump2Auto = global.get('DI18') || 0;    // 气泵2自动档位\n\n// 记录档位状态到消息\nmsg.deviceModes = {\n    waterPump1Auto: waterPump1Auto,\n    waterPump2Auto: waterPump2Auto,\n    airPump1Auto: airPump1Auto,\n    airPump2Auto: airPump2Auto\n};\n\nmsg.floatStatus = floatStatus;\n\nnode.log('=== 档位检查 ===');\nnode.log('浮球状态(DI01): ' + floatStatus);\nnode.log('水泵1自动档(DI15): ' + waterPump1Auto);\nnode.log('水泵2自动档(DI16): ' + waterPump2Auto);\nnode.log('气泵1自动档(DI17): ' + airPump1Auto);\nnode.log('气泵2自动档(DI18): ' + airPump2Auto);\n\n// 检查是否处于自动模式\nif (waterPump1Auto === 1 || waterPump2Auto === 1 || airPump1Auto === 1 || airPump2Auto === 1) {\n    node.log('>>> 设备处于自动模式，继续执行自动控制逻辑');\n    msg.canProceed = true;\n    msg.payload = floatStatus;\n    \n    // 设置故障灯状态（正常状态）\n    global.set('faultLightStatus', 0);\n    \n    return msg;\n} else {\n    node.log('>>> 设备未处于自动模式，跳过自动控制');\n    node.log('>>> 点亮故障灯提示非自动模式');\n    \n    // 设置故障灯状态（非自动模式）\n    global.set('faultLightStatus', 1);\n    \n    msg.canProceed = false;\n    return null;  // 不传递消息，停止执行\n}", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 560, "y": 100, "wires": [["float_water_pump_logic"]]}, {"id": "float_water_pump_logic", "type": "function", "z": "d3caf6daf68cc456", "name": "浮球水泵轮换逻辑", "func": "// 获取当前浮球状态\nvar currentFloat1 = parseInt(msg.payload) || 0;\n\n// 从flow context获取前一次状态\nvar previousFloat1 = flow.get('previousFloat1');\nvar nextWaterPump = flow.get('nextWaterPump') || 1;  // 下次要启动的水泵编号\nvar waterPump1Running = flow.get('waterPump1Running') || false;\nvar waterPump2Running = flow.get('waterPump2Running') || false;\n\n// 获取水泵档位状态\nvar waterPump1Auto = global.get('DI15') || 0;\nvar waterPump2Auto = global.get('DI16') || 0;\n\nmsg.currentFloat1 = currentFloat1;\nmsg.previousFloat1 = previousFloat1;\nmsg.action = 'none';\n\nnode.log('=== 浮球水泵轮换逻辑 ===');\nnode.log('当前浮球状态: ' + currentFloat1);\nnode.log('前次浮球状态: ' + previousFloat1);\nnode.log('下次启动水泵: ' + nextWaterPump);\nnode.log('水泵1运行状态: ' + waterPump1Running);\nnode.log('水泵2运行状态: ' + waterPump2Running);\nnode.log('水泵1自动档(DI15): ' + waterPump1Auto);\nnode.log('水泵2自动档(DI16): ' + waterPump2Auto);\n\n// Case 1: 浮球复位 (1 -> 0) - 关闭所有水泵\nif (currentFloat1 === 0 && previousFloat1 === 1) {\n    msg.action = 'stop_all_water_pumps';\n    msg.description = '浮球复位，关闭所有水泵';\n    \n    // 清理水泵状态\n    flow.set('waterPump1Running', false);\n    flow.set('waterPump2Running', false);\n    \n    node.log('>>> 触发动作: 浮球复位 (1→0)');\n    node.log('>>> 执行操作: 关闭所有水泵');\n}\n// Case 2: 浮球激活 (0 -> 1) - 轮换启动水泵\nelse if (currentFloat1 === 1 && (previousFloat1 === 0 || previousFloat1 === null || previousFloat1 === undefined)) {\n    // 浮球激活时，根据轮换顺序启动水泵\n    if (nextWaterPump === 1 && waterPump1Auto === 1) {\n        msg.action = 'start_water_pump1';\n        msg.description = '浮球激活，轮换启动水泵1';\n        \n        flow.set('waterPump1Running', true);\n        flow.set('nextWaterPump', 2);  // 下次启动水泵2\n        \n        node.log('>>> 触发动作: 浮球激活 (0→1) - 轮换启动水泵1');\n        node.log('>>> 下次将启动: 水泵2');\n    } else if (nextWaterPump === 2 && waterPump2Auto === 1) {\n        msg.action = 'start_water_pump2';\n        msg.description = '浮球激活，轮换启动水泵2';\n        \n        flow.set('waterPump2Running', true);\n        flow.set('nextWaterPump', 1);  // 下次启动水泵1\n        \n        node.log('>>> 触发动作: 浮球激活 (0→1) - 轮换启动水泵2');\n        node.log('>>> 下次将启动: 水泵1');\n    } else {\n        // 如果指定的水泵不在自动档，则切换到另一个\n        if (nextWaterPump === 1 && waterPump1Auto === 0 && waterPump2Auto === 1) {\n            msg.action = 'start_water_pump2';\n            msg.description = '水泵1非自动档，启动水泵2';\n            flow.set('waterPump2Running', true);\n            flow.set('nextWaterPump', 1);\n            node.log('>>> 水泵1非自动档，切换启动水泵2');\n        } else if (nextWaterPump === 2 && waterPump2Auto === 0 && waterPump1Auto === 1) {\n            msg.action = 'start_water_pump1';\n            msg.description = '水泵2非自动档，启动水泵1';\n            flow.set('waterPump1Running', true);\n            flow.set('nextWaterPump', 2);\n            node.log('>>> 水泵2非自动档，切换启动水泵1');\n        } else {\n            node.log('>>> 没有可用的自动档水泵');\n        }\n    }\n}\n// Case 3: 浮球保持激活 (1 -> 1) - 保持运行状态\nelse if (currentFloat1 === 1 && previousFloat1 === 1) {\n    node.log('>>> 浮球保持激活状态 (1→1)');\n    node.log('>>> 水泵1运行: ' + waterPump1Running);\n    node.log('>>> 水泵2运行: ' + waterPump2Running);\n    // 保持当前状态，无需操作\n} else {\n    node.log('>>> 浮球状态无变化，无需执行水泵操作');\n}\n\n// 更新前一次状态\nflow.set('previousFloat1', currentFloat1);\n\nmsg.nextWaterPump = nextWaterPump;\n\nnode.log('=== 浮球水泵控制逻辑检查完成 ===\\n');\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 760, "y": 100, "wires": [["water_pump_action_switch", "air_pump_control_logic"]]}, {"id": "water_pump_action_switch", "type": "switch", "z": "d3caf6daf68cc456", "name": "水泵动作分发", "property": "action", "propertyType": "msg", "rules": [{"t": "eq", "v": "start_water_pump1", "vt": "str"}, {"t": "eq", "v": "start_water_pump2", "vt": "str"}, {"t": "eq", "v": "stop_all_water_pumps", "vt": "str"}], "checkall": "false", "repair": false, "outputs": 3, "x": 1000, "y": 80, "wires": [["start_water_pump1_function"], ["start_water_pump2_function"], ["stop_water_pumps_function"]]}, {"id": "start_water_pump1_function", "type": "function", "z": "d3caf6daf68cc456", "name": "启动水泵1", "func": "node.log('=== 启动水泵1 ===');\nnode.log('轮换启动水泵1 (DO21)');\n\nmsg.payload = 1;\nmsg.do_name = 'DO21';\nmsg.description = '轮换启动水泵1';\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1200, "y": 40, "wires": [["do21_node"]]}, {"id": "start_water_pump2_function", "type": "function", "z": "d3caf6daf68cc456", "name": "启动水泵2", "func": "node.log('=== 启动水泵2 ===');\nnode.log('轮换启动水泵2 (DO22)');\n\nmsg.payload = 1;\nmsg.do_name = 'DO22';\nmsg.description = '轮换启动水泵2';\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1200, "y": 80, "wires": [["do22_node"]]}, {"id": "stop_water_pumps_function", "type": "function", "z": "d3caf6daf68cc456", "name": "停止所有水泵", "func": "node.log('=== 停止所有水泵 ===');\nnode.log('浮球复位，关闭DO21和DO22');\n\n// 创建关闭消息\nvar messages = [\n    { payload: 0, do_name: 'DO21', description: '停止水泵1' },\n    { payload: 0, do_name: 'DO22', description: '停止水泵2' }\n];\n\nnode.log('>>> 关闭DO21 (水泵1)');\nnode.log('>>> 关闭DO22 (水泵2)');\n\nreturn messages;", "outputs": 2, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1200, "y": 120, "wires": [["stop_do21_node"], ["stop_do22_node"]]}, {"id": "air_pump_control_logic", "type": "function", "z": "d3caf6daf68cc456", "name": "气泵30分钟交替运行逻辑", "func": "// 获取气泵档位状态\nvar airPump1Auto = global.get('DI17') || 0;  // 气泵1自动档位\nvar airPump2Auto = global.get('DI18') || 0;  // 气泵2自动档位\n\n// 获取气泵运行状态 - 如果是首次运行，从实际DO状态初始化\nvar airPump1Running = flow.get('airPump1Running');\nvar airPump2Running = flow.get('airPump2Running');\nvar doStatus = global.get('doStatus') || {};\n\n// 首次运行时初始化状态（与实际DO状态同步）\nvar isFirstInit = false;\nif (airPump1Running === undefined) {\n    airPump1Running = (doStatus.DO23 === 1);\n    flow.set('airPump1Running', airPump1Running);\n    node.log('>>> 初始化气泵1运行状态: ' + airPump1Running);\n    isFirstInit = true;\n}\nif (airPump2Running === undefined) {\n    airPump2Running = (doStatus.DO24 === 1);\n    flow.set('airPump2Running', airPump2Running);\n    node.log('>>> 初始化气泵2运行状态: ' + airPump2Running);\n    isFirstInit = true;\n}\n\n// 确保布尔类型\nairPump1Running = !! airPump1Running;\nairPump2Running = !! airPump2Running;\n\n// 首次初始化时，如果发现双泵都在运行，强制只保留气泵1\nif (isFirstInit && airPump1Running && airPump2Running) {\n    node.log('>>> 首次初始化检测到双泵运行，强制停止气泵2，只保留气泵1');\n    airPump2Running = false;\n    flow.set('airPump2Running', false);\n    // 设置需要执行停止气泵2的动作\n    msg.airPumpAction = 'stop_air_pump2_init_conflict';\n    msg.airPumpDescription = '初始化冲突：强制停止气泵2';\n    \n    // 设置运行状态\n    flow.set('currentRunningPump', 'pump1');\n    flow.set('pumpStartTime', Date.now());\n    \n    node.log('=== 气泵30分钟交替运行逻辑检查完成 ===\\n');\n    return msg;\n}\n\n// 获取气泵故障状态\nvar airPump1Fault = flow.get('airPump1Fault') || false;\nvar airPump2Fault = flow.get('airPump2Fault') || false;\n\n// 获取当前运行的气泵和启动时间\nvar currentRunningPump = flow.get('currentRunningPump') || null;  // 'pump1' 或 'pump2'\nvar pumpStartTime = flow.get('pumpStartTime') || null;\nvar currentTime = Date.now();\n\n// 30分钟 = 30 * 60 * 1000 毫秒\nvar PUMP_CYCLE_TIME = 30 * 60 * 1000;  // 30分钟交替周期\n\nmsg.airPumpAction = 'none';\n\nnode.log('=== 气泵30分钟交替运行逻辑 ===');\nnode.log('气泵1自动档(DI17): ' + airPump1Auto);\nnode.log('气泵2自动档(DI18): ' + airPump2Auto);\nnode.log('气泵1运行状态: ' + airPump1Running);\nnode.log('气泵2运行状态: ' + airPump2Running);\nnode.log('气泵1故障状态: ' + airPump1Fault);\nnode.log('气泵2故障状态: ' + airPump2Fault);\nnode.log('当前运行气泵: ' + (currentRunningPump || '无'));\n\n// 计算运行时间\nvar runTime = 0;\nif (pumpStartTime) {\n    runTime = currentTime - pumpStartTime;\n    var runMinutes = Math.floor(runTime / (60 * 1000));\n    node.log('当前气泵运行时间: ' + runMinutes + ' 分钟');\n}\n\n// 气泵控制逻辑\nif (airPump1Auto === 1 && airPump2Auto === 1) {\n    // 两个气泵都处于自动档位\n    \n    // Case 1: 气泵1故障，气泵2正常 - 确保气泵2运行\n    if (airPump1Fault && !airPump2Fault) {\n        if (!airPump2Running) {\n            msg.airPumpAction = 'start_air_pump2_fault_switch';\n            msg.airPumpDescription = '气泵1故障，启动气泵2';\n            flow.set('airPump2Running', true);\n            flow.set('currentRunningPump', 'pump2');\n            flow.set('pumpStartTime', currentTime);\n            node.log('>>> 气泵1故障，启动气泵2 (DO24)');\n        }\n        if (airPump1Running) {\n            msg.airPumpAction = 'stop_air_pump1_fault';\n            msg.airPumpDescription = '气泵1故障，停止气泵1';\n            flow.set('airPump1Running', false);\n            node.log('>>> 气泵1故障，停止气泵1 (DO23)');\n        }\n    }\n    // Case 2: 气泵2故障，气泵1正常 - 确保气泵1运行\n    else if (airPump2Fault && !airPump1Fault) {\n        if (!airPump1Running) {\n            msg.airPumpAction = 'start_air_pump1_fault_switch';\n            msg.airPumpDescription = '气泵2故障，启动气泵1';\n            flow.set('airPump1Running', true);\n            flow.set('currentRunningPump', 'pump1');\n            flow.set('pumpStartTime', currentTime);\n            node.log('>>> 气泵2故障，启动气泵1 (DO23)');\n        }\n        if (airPump2Running) {\n            msg.airPumpAction = 'stop_air_pump2_fault';\n            msg.airPumpDescription = '气泵2故障，停止气泵2';\n            flow.set('airPump2Running', false);\n            node.log('>>> 气泵2故障，停止气泵2 (DO24)');\n        }\n    }\n    // Case 3: 两个气泵都正常 - 30分钟交替运行逻辑\n    else if (!airPump1Fault && !airPump2Fault) {\n        // 检测到双泵运行的异常情况 - 在正常交替模式下应该只有一个泵运行\n        if (airPump1Running && airPump2Running) {\n            node.log('>>> 检测到异常：两个气泵都在运行，将停止气泵2，保持气泵1运行');\n            msg.airPumpAction = 'stop_air_pump2_fault';\n            msg.airPumpDescription = '异常双泵运行，停止气泵2';\n            flow.set('airPump2Running', false);\n            flow.set('currentRunningPump', 'pump1');\n            if (!pumpStartTime) {\n                flow.set('pumpStartTime', currentTime);\n            }\n        }\n        // 如果没有气泵运行，启动气泵1开始循环\n        else if (!airPump1Running && !airPump2Running) {\n            msg.airPumpAction = 'start_air_pump1_cycle';\n            msg.airPumpDescription = '开始气泵交替循环，启动气泵1';\n            flow.set('airPump1Running', true);\n            flow.set('currentRunningPump', 'pump1');\n            flow.set('pumpStartTime', currentTime);\n            node.log('>>> 开始30分钟交替循环，启动气泵1 (DO23)');\n        }\n        // 检查是否需要切换气泵（30分钟时间到）\n        else if (pumpStartTime && runTime >= PUMP_CYCLE_TIME) {\n            if (currentRunningPump === 'pump1' && airPump1Running) {\n                // 从气泵1切换到气泵2\n                msg.airPumpAction = 'switch_to_pump2';\n                msg.airPumpDescription = '30分钟时间到，从气泵1切换到气泵2';\n                \n                flow.set('airPump1Running', false);\n                flow.set('airPump2Running', true);\n                flow.set('currentRunningPump', 'pump2');\n                flow.set('pumpStartTime', currentTime);\n                \n                node.log('>>> 30分钟时间到，从气泵1切换到气泵2');\n            }\n            else if (currentRunningPump === 'pump2' && airPump2Running) {\n                // 从气泵2切换到气泵1\n                msg.airPumpAction = 'switch_to_pump1';\n                msg.airPumpDescription = '30分钟时间到，从气泵2切换到气泵1';\n                \n                flow.set('airPump2Running', false);\n                flow.set('airPump1Running', true);\n                flow.set('currentRunningPump', 'pump1');\n                flow.set('pumpStartTime', currentTime);\n                \n                node.log('>>> 30分钟时间到，从气泵2切换到气泵1');\n            }\n        }\n        // 正常运行中，保持当前状态\n        else {\n            var remainingMinutes = Math.floor((PUMP_CYCLE_TIME - runTime) / (60 * 1000));\n            if (remainingMinutes >= 0) {\n                node.log('>>> 气泵正常运行中，距离下次切换还有 ' + remainingMinutes + ' 分钟');\n            }\n        }\n    }\n    // Case 4: 两个气泵都故障 - 停止所有\n    else if (airPump1Fault && airPump2Fault) {\n        msg.airPumpAction = 'stop_all_air_pumps_fault';\n        msg.airPumpDescription = '两个气泵都故障，停止所有';\n        flow.set('airPump1Running', false);\n        flow.set('airPump2Running', false);\n        flow.set('currentRunningPump', null);\n        flow.set('pumpStartTime', null);\n        node.log('>>> 两个气泵都故障，停止所有气泵');\n    }\n}\nelse if (airPump1Auto === 1 && airPump2Auto === 0) {\n    // 只有气泵1处于自动档位\n    if (!airPump1Fault && !airPump1Running) {\n        msg.airPumpAction = 'start_air_pump1_only';\n        msg.airPumpDescription = '仅气泵1自动档，启动气泵1';\n        flow.set('airPump1Running', true);\n        flow.set('currentRunningPump', 'pump1');\n        flow.set('pumpStartTime', currentTime);\n        node.log('>>> 仅气泵1自动档，启动气泵1 (DO23)');\n    }\n    if (airPump2Running) {\n        msg.airPumpAction = 'stop_air_pump2_not_auto';\n        msg.airPumpDescription = '气泵2非自动档，停止气泵2';\n        flow.set('airPump2Running', false);\n        node.log('>>> 气泵2非自动档，停止气泵2 (DO24)');\n    }\n}\nelse if (airPump1Auto === 0 && airPump2Auto === 1) {\n    // 只有气泵2处于自动档位\n    if (!airPump2Fault && !airPump2Running) {\n        msg.airPumpAction = 'start_air_pump2_only';\n        msg.airPumpDescription = '仅气泵2自动档，启动气泵2';\n        flow.set('airPump2Running', true);\n        flow.set('currentRunningPump', 'pump2');\n        flow.set('pumpStartTime', currentTime);\n        node.log('>>> 仅气泵2自动档，启动气泵2 (DO24)');\n    }\n    if (airPump1Running) {\n        msg.airPumpAction = 'stop_air_pump1_not_auto';\n        msg.airPumpDescription = '气泵1非自动档，停止气泵1';\n        flow.set('airPump1Running', false);\n        node.log('>>> 气泵1非自动档，停止气泵1 (DO23)');\n    }\n}\nelse {\n    // 两个气泵都不在自动档位，停止所有并清理状态\n    if (airPump1Running || airPump2Running) {\n        msg.airPumpAction = 'stop_all_air_pumps_not_auto';\n        msg.airPumpDescription = '气泵都不在自动档，停止所有';\n        flow.set('airPump1Running', false);\n        flow.set('airPump2Running', false);\n        flow.set('currentRunningPump', null);\n        flow.set('pumpStartTime', null);\n        node.log('>>> 气泵都不在自动档，停止所有气泵');\n    }\n}\n\nnode.log('=== 气泵30分钟交替运行逻辑检查完成 ===\\n');\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1000, "y": 200, "wires": [["air_pump_action_switch"]]}, {"id": "air_pump_action_switch", "type": "switch", "z": "d3caf6daf68cc456", "name": "气泵动作分发", "property": "airPumpAction", "propertyType": "msg", "rules": [{"t": "eq", "v": "start_air_pump1_fault_switch", "vt": "str"}, {"t": "eq", "v": "start_air_pump2_fault_switch", "vt": "str"}, {"t": "eq", "v": "start_air_pump1_continuous", "vt": "str"}, {"t": "eq", "v": "start_air_pump2_continuous", "vt": "str"}, {"t": "eq", "v": "start_air_pump1_cycle", "vt": "str"}, {"t": "eq", "v": "start_air_pump2_cycle", "vt": "str"}, {"t": "eq", "v": "switch_to_pump1", "vt": "str"}, {"t": "eq", "v": "switch_to_pump2", "vt": "str"}, {"t": "eq", "v": "start_air_pump1_only", "vt": "str"}, {"t": "eq", "v": "start_air_pump2_only", "vt": "str"}, {"t": "eq", "v": "stop_air_pump1_fault", "vt": "str"}, {"t": "eq", "v": "stop_air_pump2_fault", "vt": "str"}, {"t": "eq", "v": "stop_all_air_pumps_fault", "vt": "str"}, {"t": "eq", "v": "stop_all_air_pumps_not_auto", "vt": "str"}, {"t": "eq", "v": "stop_air_pump2_init_conflict", "vt": "str"}], "checkall": "false", "repair": false, "outputs": 15, "x": 1280, "y": 200, "wires": [["start_air_pump1_function"], ["start_air_pump2_function"], ["start_air_pump1_function"], ["start_air_pump2_function"], ["start_air_pump1_function"], ["start_air_pump2_function"], ["switch_air_pumps_function"], ["switch_air_pumps_function"], ["start_air_pump1_function"], ["start_air_pump2_function"], ["stop_air_pump1_function"], ["stop_air_pump2_function"], ["stop_all_air_pumps_function"], ["stop_all_air_pumps_function"], ["stop_air_pump2_function"]]}, {"id": "start_air_pump1_function", "type": "function", "z": "d3caf6daf68cc456", "name": "启动气泵1", "func": "node.log('=== 启动气泵1 ===');\nnode.log('启动气泵1 (DO23): ' + (msg.airPumpDescription || ''));\n\nmsg.payload = 1;\nmsg.do_name = 'DO23';\nmsg.description = msg.airPumpDescription || '启动气泵1';\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1500, "y": 160, "wires": [["do23_node"]]}, {"id": "start_air_pump2_function", "type": "function", "z": "d3caf6daf68cc456", "name": "启动气泵2", "func": "node.log('=== 启动气泵2 ===');\nnode.log('启动气泵2 (DO24): ' + (msg.airPumpDescription || ''));\n\nmsg.payload = 1;\nmsg.do_name = 'DO24';\nmsg.description = msg.airPumpDescription || '启动气泵2';\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1500, "y": 200, "wires": [["do24_node"]]}, {"id": "stop_air_pump1_function", "type": "function", "z": "d3caf6daf68cc456", "name": "停止气泵1", "func": "node.log('=== 停止气泵1 ===');\nnode.log('停止气泵1 (DO23): ' + (msg.airPumpDescription || ''));\n\nmsg.payload = 0;\nmsg.do_name = 'DO23';\nmsg.description = msg.airPumpDescription || '停止气泵1';\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1500, "y": 240, "wires": [["do23_node"]]}, {"id": "stop_air_pump2_function", "type": "function", "z": "d3caf6daf68cc456", "name": "停止气泵2", "func": "node.log('=== 停止气泵2 ===');\nnode.log('停止气泵2 (DO24): ' + (msg.airPumpDescription || ''));\n\nmsg.payload = 0;\nmsg.do_name = 'DO24';\nmsg.description = msg.airPumpDescription || '停止气泵2';\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1500, "y": 280, "wires": [["do24_node"]]}, {"id": "stop_all_air_pumps_function", "type": "function", "z": "d3caf6daf68cc456", "name": "停止所有气泵", "func": "node.log('=== 停止所有气泵 ===');\nnode.log('停止所有气泵: ' + (msg.airPumpDescription || ''));\n\n// 创建关闭消息\nvar messages = [\n    { payload: 0, do_name: 'DO23', description: msg.airPumpDescription || '停止气泵1' },\n    { payload: 0, do_name: 'DO24', description: msg.airPumpDescription || '停止气泵2' }\n];\n\nreturn messages;", "outputs": 2, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1500, "y": 320, "wires": [["do23_node"], ["do24_node"]]}, {"id": "switch_air_pumps_function", "type": "function", "z": "d3caf6daf68cc456", "name": "气泵切换控制", "func": "// 处理30分钟气泵切换逻辑\nvar action = msg.airPumpAction || '';\nvar description = msg.airPumpDescription || '';\n\nnode.log('=== 气泵切换控制 ===');\nnode.log('切换动作: ' + action);\nnode.log('切换说明: ' + description);\n\nvar messages = [];\n\nif (action === 'switch_to_pump2') {\n    // 从气泵1切换到气泵2：先停止气泵1，再启动气泵2\n    messages = [\n        { payload: 0, do_name: 'DO23', description: '30分钟切换：停止气泵1' },\n        { payload: 1, do_name: 'DO24', description: '30分钟切换：启动气泵2' }\n    ];\n    node.log('>>> 执行切换：停止气泵1 -> 启动气泵2');\n} else if (action === 'switch_to_pump1') {\n    // 从气泵2切换到气泵1：先停止气泵2，再启动气泵1\n    messages = [\n        { payload: 0, do_name: 'DO24', description: '30分钟切换：停止气泵2' },\n        { payload: 1, do_name: 'DO23', description: '30分钟切换：启动气泵1' }\n    ];\n    node.log('>>> 执行切换：停止气泵2 -> 启动气泵1');\n} else {\n    node.log('>>> 未知的切换动作: ' + action);\n    return null;\n}\n\nnode.log('=== 气泵切换控制完成 ===\\n');\n\nreturn messages;", "outputs": 2, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1500, "y": 360, "wires": [["do23_node"], ["do24_node"]]}, {"id": "do21_node", "type": "edge set node", "z": "d3caf6daf68cc456", "name": "DO21控制(水泵1)", "nodeid": "DO21", "datatype": "INT", "payload": "", "payloadType": "msg", "x": 1400, "y": 40, "wires": [["do21_result_debug"]]}, {"id": "do22_node", "type": "edge set node", "z": "d3caf6daf68cc456", "name": "DO22控制(水泵2)", "nodeid": "DO22", "datatype": "INT", "payload": "", "payloadType": "msg", "x": 1400, "y": 80, "wires": [["do22_result_debug"]]}, {"id": "do23_node", "type": "edge set node", "z": "d3caf6daf68cc456", "name": "DO23控制(气泵1)", "nodeid": "DO23", "datatype": "INT", "payload": "", "payloadType": "msg", "x": 1700, "y": 200, "wires": [["do23_result_debug"]]}, {"id": "do24_node", "type": "edge set node", "z": "d3caf6daf68cc456", "name": "DO24控制(气泵2)", "nodeid": "DO24", "datatype": "INT", "payload": "", "payloadType": "msg", "x": 1700, "y": 240, "wires": [["do24_result_debug"]]}, {"id": "stop_do21_node", "type": "edge set node", "z": "d3caf6daf68cc456", "name": "停止DO21", "nodeid": "DO21", "datatype": "INT", "payload": "0", "x": 1400, "y": 120, "wires": [["stop_result_debug"]]}, {"id": "stop_do22_node", "type": "edge set node", "z": "d3caf6daf68cc456", "name": "停止DO22", "nodeid": "DO22", "datatype": "INT", "payload": "0", "x": 1400, "y": 160, "wires": [["stop_result_debug"]]}, {"id": "do21_result_debug", "type": "debug", "z": "d3caf6daf68cc456", "name": "DO21操作结果", "active": true, "tosidebar": true, "console": true, "tostatus": false, "complete": "description", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 1600, "y": 40, "wires": []}, {"id": "do22_result_debug", "type": "debug", "z": "d3caf6daf68cc456", "name": "DO22操作结果", "active": true, "tosidebar": true, "console": true, "tostatus": false, "complete": "description", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 1600, "y": 80, "wires": []}, {"id": "do23_result_debug", "type": "debug", "z": "d3caf6daf68cc456", "name": "DO23操作结果", "active": true, "tosidebar": true, "console": true, "tostatus": false, "complete": "description", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 1900, "y": 200, "wires": []}, {"id": "do24_result_debug", "type": "debug", "z": "d3caf6daf68cc456", "name": "DO24操作结果", "active": true, "tosidebar": true, "console": true, "tostatus": false, "complete": "description", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 1900, "y": 240, "wires": []}, {"id": "stop_result_debug", "type": "debug", "z": "d3caf6daf68cc456", "name": "停止操作结果", "active": true, "tosidebar": true, "console": true, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1600, "y": 140, "wires": []}, {"id": "mode_status_inject", "type": "inject", "z": "d3caf6daf68cc456", "name": "读取档位状态", "props": [{"p": "payload"}], "repeat": "1", "crontab": "", "once": true, "onceDelay": 0.5, "topic": "", "payload": "", "payloadType": "date", "x": 160, "y": 280, "wires": [["read_di15_node"]]}, {"id": "read_di15_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "读取DI15(水泵1自动)", "nodeid": "DI15", "datatype": "INT", "x": 360, "y": 280, "wires": [["store_di15_function"]]}, {"id": "store_di15_function", "type": "function", "z": "d3caf6daf68cc456", "name": "存储DI15状态", "func": "global.set('DI15', parseInt(msg.payload) || 0);\nnode.log('DI15(水泵1自动档): ' + msg.payload);\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 560, "y": 280, "wires": [["read_di16_node"]]}, {"id": "read_di16_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "读取DI16(水泵2自动)", "nodeid": "DI16", "datatype": "INT", "x": 760, "y": 280, "wires": [["store_di16_function"]]}, {"id": "store_di16_function", "type": "function", "z": "d3caf6daf68cc456", "name": "存储DI16状态", "func": "global.set('DI16', parseInt(msg.payload) || 0);\nnode.log('DI16(水泵2自动档): ' + msg.payload);\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 960, "y": 280, "wires": [["read_di17_node"]]}, {"id": "read_di17_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "读取DI17(气泵1自动)", "nodeid": "DI17", "datatype": "INT", "x": 1160, "y": 280, "wires": [["store_di17_function"]]}, {"id": "store_di17_function", "type": "function", "z": "d3caf6daf68cc456", "name": "存储DI17状态", "func": "global.set('DI17', parseInt(msg.payload) || 0);\nnode.log('DI17(气泵1自动档): ' + msg.payload);\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1360, "y": 280, "wires": [["read_di18_node"]]}, {"id": "read_di18_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "读取DI18(气泵2自动)", "nodeid": "DI18", "datatype": "INT", "x": 1560, "y": 280, "wires": [["store_di18_function"]]}, {"id": "store_di18_function", "type": "function", "z": "d3caf6daf68cc456", "name": "存储DI18状态", "func": "global.set('DI18', parseInt(msg.payload) || 0);\nnode.log('DI18(气泵2自动档): ' + msg.payload);", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1760, "y": 280, "wires": []}, {"id": "fault_light_inject", "type": "inject", "z": "d3caf6daf68cc456", "name": "故障灯控制检查", "props": [{"p": "payload"}], "repeat": "5", "crontab": "", "once": true, "onceDelay": 1, "topic": "", "payload": "", "payloadType": "date", "x": 180, "y": 360, "wires": [["fault_light_function"]]}, {"id": "fault_light_function", "type": "function", "z": "d3caf6daf68cc456", "name": "故障灯状态处理", "func": "// 获取故障灯状态\nvar faultLightStatus = global.get('faultLightStatus') || 0;\nnode.log('故障灯状态: ' + faultLightStatus);\n\nmsg.payload = faultLightStatus;\nmsg.description = faultLightStatus ? '故障灯点亮' : '故障灯熄灭';\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 380, "y": 360, "wires": [["fault_light_set_node"]]}, {"id": "fault_light_set_node", "type": "edge set node", "z": "d3caf6daf68cc456", "name": "控制故障灯(DO01)", "nodeid": "DO01", "datatype": "INT", "x": 580, "y": 360, "wires": [["fault_light_debug"]]}, {"id": "fault_light_debug", "type": "debug", "z": "d3caf6daf68cc456", "name": "故障灯状态", "active": true, "tosidebar": true, "console": true, "tostatus": false, "complete": "description", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 780, "y": 360, "wires": []}, {"id": "manual_test_inject", "type": "inject", "z": "d3caf6daf68cc456", "name": "手动触发检查", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 170, "y": 200, "wires": [["get_float1_node"]]}, {"id": "sn_imei_init_inject", "type": "inject", "z": "d3caf6daf68cc456", "name": "读取SN/IMEI(启动一次)", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": true, "onceDelay": 0.8, "topic": "", "payload": "", "payloadType": "str", "x": 170, "y": 460, "wires": [["get_sn_node", "get_imei_node"]]}, {"id": "get_sn_node", "type": "get sn", "z": "d3caf6daf68cc456", "name": "获取设备SN", "x": 380, "y": 440, "wires": [["store_sn_function"]]}, {"id": "get_imei_node", "type": "get imei", "z": "d3caf6daf68cc456", "name": "获取设备IMEI", "x": 380, "y": 480, "wires": [["store_imei_function"]]}, {"id": "store_sn_function", "type": "function", "z": "d3caf6daf68cc456", "name": "存储SN到global", "func": "var sn = (msg.payload || '').toString();\nif (sn) {\n    global.set('sn', sn);\n    node.log('设备SN读取成功: ' + sn);\n}\nreturn null;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 600, "y": 440, "wires": [[]]}, {"id": "store_imei_function", "type": "function", "z": "d3caf6daf68cc456", "name": "存储IMEI到global", "func": "var imei = (msg.payload || '').toString();\nif (imei) {\n    global.set('imei', imei);\n    node.log('设备IMEI读取成功: ' + imei);\n}\nreturn null;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 610, "y": 480, "wires": [[]]}, {"id": "config_inject", "type": "inject", "z": "d3caf6daf68cc456", "name": "系统初始化配置", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": true, "onceDelay": 1, "topic": "", "payload": "init", "payloadType": "str", "x": 180, "y": 420, "wires": [["config_function"]]}, {"id": "config_function", "type": "function", "z": "d3caf6daf68cc456", "name": "初始化系统状态", "func": "// 初始化系统状态变量\nflow.set('previousFloat1', null);           // 前次浮球状态\nflow.set('nextWaterPump', 1);               // 下一个要启动的水泵（轮换用）\nflow.set('waterPump1Running', false);       // 水泵1运行状态\nflow.set('waterPump2Running', false);       // 水泵2运行状态\nflow.set('airPump1Running', false);         // 气泵1运行状态\nflow.set('airPump2Running', false);         // 气泵2运行状态\nflow.set('airPump1Fault', false);           // 气泵1故障状态\nflow.set('airPump2Fault', false);           // 气泵2故障状态\n\n// 重置气泵运行管理状态\nflow.set('currentRunningPump', null);       // 当前运行的气泵\nflow.set('pumpStartTime', null);            // 气泵启动时间\n\n// 初始化全局变量\nglobal.set('DI15', 0);  // 水泵1自动档位\nglobal.set('DI16', 0);  // 水泵2自动档位\nglobal.set('DI17', 0);  // 气泵1自动档位\nglobal.set('DI18', 0);  // 气泵2自动档位\nglobal.set('faultLightStatus', 0);  // 故障灯状态\nglobal.set('currentDI01', 0);       // 当前DI01状态\n\n// 预置sn/imei为空，等待启动读入\nglobal.set('sn', global.get('sn') || '');\nglobal.set('imei', global.get('imei') || '');\n\n// 清理可能存在的DO状态缓存\nglobal.set('doStatus', {});\n\nmsg.payload = '系统初始化完成';\nnode.log('=== 系统初始化 ===');\nnode.log('所有状态变量已重置');\nnode.log('水泵轮换从水泵1开始');\nnode.log('气泵故障状态已清零');\nnode.log('气泵运行管理状态已重置');\nnode.log('全局DI/DO状态已清理');\nnode.log('系统准备就绪');\nnode.log('=========================\\n');\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 380, "y": 420, "wires": [["config_debug"]]}, {"id": "config_debug", "type": "debug", "z": "d3caf6daf68cc456", "name": "配置信息", "active": true, "tosidebar": true, "console": true, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 580, "y": 420, "wires": []}, {"id": "status_debug", "type": "debug", "z": "d3caf6daf68cc456", "name": "状态详情输出", "active": true, "tosidebar": true, "console": true, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1000, "y": 140, "wires": []}, {"id": "fault_simulation_inject", "type": "inject", "z": "d3caf6daf68cc456", "name": "模拟气泵1故障", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "pump1_fault", "payloadType": "str", "x": 180, "y": 480, "wires": [["fault_simulation_function"]]}, {"id": "fault_simulation_recover1_inject", "type": "inject", "z": "d3caf6daf68cc456", "name": "气泵1故障恢复", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "pump1_recover", "payloadType": "str", "x": 180, "y": 520, "wires": [["fault_simulation_function"]]}, {"id": "fault_simulation_pump2_inject", "type": "inject", "z": "d3caf6daf68cc456", "name": "模拟气泵2故障", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "pump2_fault", "payloadType": "str", "x": 180, "y": 560, "wires": [["fault_simulation_function"]]}, {"id": "fault_simulation_recover2_inject", "type": "inject", "z": "d3caf6daf68cc456", "name": "气泵2故障恢复", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "pump2_recover", "payloadType": "str", "x": 180, "y": 600, "wires": [["fault_simulation_function"]]}, {"id": "fault_simulation_function", "type": "function", "z": "d3caf6daf68cc456", "name": "气泵故障模拟", "func": "var action = msg.payload || '';\n\nnode.log('=== 气泵故障模拟 ===');\nnode.log('执行动作: ' + action);\n\nswitch(action) {\n    case 'pump1_fault':\n        flow.set('airPump1Fault', true);\n        node.log('>>> 设置气泵1故障状态: true');\n        msg.description = '气泵1故障模拟激活';\n        break;\n        \n    case 'pump1_recover':\n        flow.set('airPump1Fault', false);\n        node.log('>>> 设置气泵1故障状态: false');\n        msg.description = '气泵1故障恢复';\n        break;\n        \n    case 'pump2_fault':\n        flow.set('airPump2Fault', true);\n        node.log('>>> 设置气泵2故障状态: true');\n        msg.description = '气泵2故障模拟激活';\n        break;\n        \n    case 'pump2_recover':\n        flow.set('airPump2Fault', false);\n        node.log('>>> 设置气泵2故障状态: false');\n        msg.description = '气泵2故障恢复';\n        break;\n        \n    default:\n        msg.description = '未知故障模拟动作';\n        break;\n}\n\nvar pump1Fault = flow.get('airPump1Fault') || false;\nvar pump2Fault = flow.get('airPump2Fault') || false;\n\nnode.log('当前故障状态:');\nnode.log('  - 气泵1故障: ' + pump1Fault);\nnode.log('  - 气泵2故障: ' + pump2Fault);\nnode.log('=========================\\n');\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 400, "y": 540, "wires": [["fault_simulation_debug"]]}, {"id": "fault_simulation_debug", "type": "debug", "z": "d3caf6daf68cc456", "name": "故障模拟状态", "active": true, "tosidebar": true, "console": true, "tostatus": false, "complete": "description", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 620, "y": 540, "wires": []}, {"id": "comment_node", "type": "comment", "z": "d3caf6daf68cc456", "name": "水泵气泵轮换控制系统说明", "info": "本工作流实现浮球激活时的水泵轮换启动和气泵故障切换逻辑：\n\n## 水泵轮换逻辑：\n1. 浮球激活时，根据轮换顺序启动水泵（首次启动水泵1，下次启动水泵2）\n2. 浮球复位时，立即停止所有水泵\n3. 只有处于自动档位的水泵才会被启动\n\n## 气泵故障切换和连续运行逻辑：\n1. 气泵故障时，好的气泵一直运行\n2. 关闭一个气泵时，另一个气泵也要一直运行（连续运行要求）\n3. 两个气泵都正常时，确保至少一个运行\n4. 支持气泵故障模拟和恢复测试\n\n## 自动档位要求：\n- 只在设备处于自动档位时执行控制逻辑\n- 非自动模式时点亮故障灯提示\n\n## 硬件接口：\n- DI01: 浮球开关状态\n- DI15-DI18: 设备自动档位状态\n- DO21-DO22: 水泵1、水泵2控制\n- DO23-DO24: 气泵1、气泵2控制\n- DO01: 故障指示灯\n\n## 数据采集和上报：\n- 电流采集2(dianliucaiji2): 4路电流数据\n- 温湿度(wenshidu): 温度和湿度数据\n- 电能表(diannengbiao): 电压、电流、功率、电能数据\n- DI/DO状态采集和上报到服务器\n\n## 调试功能：\n- 手动触发检查\n- 气泵故障模拟\n- 详细的控制台日志输出\n- 系统状态初始化", "x": 170, "y": 40, "wires": []}, {"id": "data_collection_inject", "type": "inject", "z": "d3caf6daf68cc456", "name": "数据采集定时器", "props": [{"p": "payload"}], "repeat": "5", "crontab": "", "once": true, "onceDelay": 2, "topic": "", "payload": "", "payloadType": "date", "x": 160, "y": 700, "wires": [["collect_dianliucaiji2_function"]]}, {"id": "collect_dianliucaiji2_function", "type": "function", "z": "d3caf6daf68cc456", "name": "采集电流数据", "func": "// 启动电流采集2数据收集\nnode.log('=== 开始采集电流数据(dianliucaiji2) ===');\n\n// 创建多个输出消息用于采集不同的电流通道\nvar messages = [\n    { payload: '', channel: 'curr2_ch1', deviceName: 'dianliucaiji2' },\n    { payload: '', channel: 'curr2_ch2', deviceName: 'dianliucaiji2' },\n    { payload: '', channel: 'curr2_ch3', deviceName: 'dianliucaiji2' },\n    { payload: '', channel: 'curr2_ch4', deviceName: 'dianliucaiji2' }\n];\n\nnode.log('准备采集4路电流数据: curr2_ch1-curr2_ch4');\n\nreturn messages;", "outputs": 4, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 360, "y": 700, "wires": [["get_curr2_ch1_node"], ["get_curr2_ch2_node"], ["get_curr2_ch3_node"], ["get_curr2_ch4_node"]]}, {"id": "get_curr2_ch1_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "获取curr2_ch1", "nodeid": "curr2_ch1", "datatype": "FLOAT", "x": 560, "y": 660, "wires": [["process_current_data_function"]]}, {"id": "get_curr2_ch2_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "获取curr2_ch2", "nodeid": "curr2_ch2", "datatype": "FLOAT", "x": 560, "y": 700, "wires": [["process_current_data_function"]]}, {"id": "get_curr2_ch3_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "获取curr2_ch3", "nodeid": "curr2_ch3", "datatype": "FLOAT", "x": 560, "y": 740, "wires": [["process_current_data_function"]]}, {"id": "get_curr2_ch4_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "获取curr2_ch4", "nodeid": "curr2_ch4", "datatype": "FLOAT", "x": 560, "y": 780, "wires": [["process_current_data_function"]]}, {"id": "process_current_data_function", "type": "function", "z": "d3caf6daf68cc456", "name": "处理电流数据", "func": "// 处理电流数据并存储到全局变量\nvar channel = msg.channel || 'unknown';\nvar value = parseFloat(msg.payload) || 0;\n\nnode.log('电流数据 ' + channel + ': ' + value);\n\n// 存储到全局变量\nvar currentData = global.get('dianliucaiji2') || {};\ncurrentData[channel] = value;\ncurrentData.timestamp = Date.now();\nglobal.set('dianliucaiji2', currentData);\n\nmsg.dataType = 'current';\nmsg.device = 'dianliucaiji2';\nmsg.channel = channel;\nmsg.value = value;\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 760, "y": 720, "wires": [["collect_temperature_humidity_function"]]}, {"id": "collect_temperature_humidity_function", "type": "function", "z": "d3caf6daf68cc456", "name": "触发温湿度采集", "func": "// 每当收到电流数据时，触发温湿度采集（避免重复触发）\nif (msg.channel === 'curr2_ch1') {\n    node.log('=== 开始采集温湿度数据(wenshidu) ===');\n    \n    var messages = [\n        { payload: '', dataType: 'humidity', deviceName: 'wenshidu' },\n        { payload: '', dataType: 'temperature', deviceName: 'wenshidu' }\n    ];\n    \n    return messages;\n}\n\n// 其他电流通道数据不触发温湿度采集\nreturn null;", "outputs": 2, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 980, "y": 720, "wires": [["get_humidity_node"], ["get_temperature_node"]]}, {"id": "get_humidity_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "获取湿度", "nodeid": "humiditye", "datatype": "FLOAT", "x": 1200, "y": 700, "wires": [["process_temp_humid_function"]]}, {"id": "get_temperature_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "获取温度", "nodeid": "temperature", "datatype": "FLOAT", "x": 1200, "y": 740, "wires": [["process_temp_humid_function"]]}, {"id": "process_temp_humid_function", "type": "function", "z": "d3caf6daf68cc456", "name": "处理温湿度数据", "func": "// 处理温湿度数据并存储到全局变量\nvar dataType = msg.dataType || 'unknown';\nvar value = parseFloat(msg.payload) || 0;\n\nnode.log('温湿度数据 ' + dataType + ': ' + value);\n\n// 存储到全局变量\nvar tempHumidData = global.get('wenshidu') || {};\nif (dataType === 'humidity') {\n    tempHumidData.humidity = value;\n} else if (dataType === 'temperature') {\n    tempHumidData.temperature = value;\n}\ntempHumidData.timestamp = Date.now();\nglobal.set('wenshidu', tempHumidData);\n\nmsg.device = 'wenshidu';\nmsg.value = value;\n\n// 只有温度数据触发电能表采集\nif (dataType === 'temperature') {\n    return msg;\n}\n\nreturn null;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1400, "y": 720, "wires": [["collect_power_meter_function"]]}, {"id": "collect_power_meter_function", "type": "function", "z": "d3caf6daf68cc456", "name": "触发电能表采集", "func": "node.log('=== 开始采集电能表数据(diannengbiao) ===');\n\n// 创建消息用于采集关键的电能表数据\nvar messages = [\n    { payload: '', param: 'Voltage_Ua', deviceName: 'diannengbiao' },\n    { payload: '', param: 'Voltage_Ub', deviceName: 'diannengbiao' },\n    { payload: '', param: 'Voltage_Uc', deviceName: 'diannengbiao' },\n    { payload: '', param: 'Current_Ia', deviceName: 'diannengbiao' },\n    { payload: '', param: 'Current_Ib', deviceName: 'diannengbiao' },\n    { payload: '', param: 'Current_Ic', deviceName: 'diannengbiao' },\n    { payload: '', param: 'ActivePower_Pt', deviceName: 'diannengbiao' },\n    { payload: '', param: 'ActivePower_Pa', deviceName: 'diannengbiao' },\n    { payload: '', param: 'ActivePower_Pb', deviceName: 'diannengbiao' },\n    { payload: '', param: 'ActivePower_Pc', deviceName: 'diannengbiao' },\n    { payload: '', param: 'ImpEp', deviceName: 'diannengbiao' }\n];\n\nnode.log('准备采集电能表完整数据: A/B/C三相电压、A/B/C三相电流、合相功率、A/B/C相功率、电能');\n\nreturn messages;", "outputs": 11, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1620, "y": 720, "wires": [["get_voltage_ua_node"], ["get_voltage_ub_node"], ["get_voltage_uc_node"], ["get_current_ia_node"], ["get_current_ib_node"], ["get_current_ic_node"], ["get_active_power_node"], ["get_active_power_pa_node"], ["get_active_power_pb_node"], ["get_active_power_pc_node"], ["get_energy_node"]]}, {"id": "get_voltage_ua_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "获取电压Ua", "nodeid": "Voltage_Ua", "datatype": "FLOAT", "x": 1840, "y": 680, "wires": [["process_power_meter_function"]]}, {"id": "get_voltage_ub_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "获取电压Ub", "nodeid": "Voltage_Ub", "datatype": "FLOAT", "x": 1840, "y": 720, "wires": [["process_power_meter_function"]]}, {"id": "get_voltage_uc_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "获取电压Uc", "nodeid": "Voltage_Uc", "datatype": "FLOAT", "x": 1840, "y": 760, "wires": [["process_power_meter_function"]]}, {"id": "get_current_ia_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "获取电流Ia", "nodeid": "Current_Ia", "datatype": "FLOAT", "x": 1840, "y": 800, "wires": [["process_power_meter_function"]]}, {"id": "get_current_ib_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "获取电流Ib", "nodeid": "Current_Ib", "datatype": "FLOAT", "x": 1840, "y": 840, "wires": [["process_power_meter_function"]]}, {"id": "get_current_ic_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "获取电流Ic", "nodeid": "Current_Ic", "datatype": "FLOAT", "x": 1840, "y": 880, "wires": [["process_power_meter_function"]]}, {"id": "get_active_power_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "获取有功功率", "nodeid": "ActivePower_Pt", "datatype": "FLOAT", "x": 1840, "y": 920, "wires": [["process_power_meter_function"]]}, {"id": "get_energy_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "获取电能", "nodeid": "ImpEp", "datatype": "FLOAT", "x": 1840, "y": 1040, "wires": [["process_power_meter_function"]]}, {"id": "get_active_power_pa_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "获取A相有功功率", "nodeid": "ActivePower_Pa", "datatype": "FLOAT", "x": 1840, "y": 960, "wires": [["process_power_meter_function"]]}, {"id": "get_active_power_pb_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "获取B相有功功率", "nodeid": "ActivePower_Pb", "datatype": "FLOAT", "x": 1840, "y": 1000, "wires": [["process_power_meter_function"]]}, {"id": "get_active_power_pc_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "获取C相有功功率", "nodeid": "ActivePower_Pc", "datatype": "FLOAT", "x": 1840, "y": 1080, "wires": [["process_power_meter_function"]]}, {"id": "process_power_meter_function", "type": "function", "z": "d3caf6daf68cc456", "name": "处理电能表数据", "func": "// 处理电能表数据并存储到全局变量\nvar param = msg.param || 'unknown';\nvar value = parseFloat(msg.payload) || 0;\n\nnode.log('电能表数据 ' + param + ': ' + value);\n\n// 存储到全局变量\nvar powerMeterData = global.get('diannengbiao') || {};\npowerMeterData[param] = value;\npowerMeterData.timestamp = Date.now();\nglobal.set('diannengbiao', powerMeterData);\n\nmsg.device = 'diannengbiao';\nmsg.param = param;\nmsg.value = value;\n\n// 只有电能数据触发DI/DO状态采集\nif (param === 'ImpEp') {\n    return msg;\n}\n\nreturn null;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 2040, "y": 740, "wires": [["collect_di_do_status_function"]]}, {"id": "collect_di_do_status_function", "type": "function", "z": "d3caf6daf68cc456", "name": "采集DI/DO状态", "func": "node.log('=== 开始采集DI/DO状态 ===');\n\n// 创建消息用于采集DI和DO状态\nvar messages = [\n    { payload: '', statusType: 'DI', points: ['DI01', 'DI15', 'DI16', 'DI17', 'DI18'] },\n    { payload: '', statusType: 'DO', points: ['DO21', 'DO22', 'DO23', 'DO24', 'DO01'] }\n];\n\nnode.log('准备采集DI状态: DI01,DI15-DI18');\nnode.log('准备采集DO状态: DO21-DO24,DO01');\n\nreturn messages;", "outputs": 2, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 2260, "y": 740, "wires": [["get_di_status_function"], ["get_do_status_function"]]}, {"id": "get_di_status_function", "type": "function", "z": "d3caf6daf68cc456", "name": "获取DI状态", "func": "// 获取DI状态并存储\nvar diStatus = {};\nvar points = msg.points || [];\n\nnode.log('=== 采集DI状态 ===');\n\n// 从全局变量中获取已存储的DI状态\nfor (var i = 0; i < points.length; i++) {\n    var point = points[i];\n    var value = 0;\n    \n    if (point === 'DI01') {\n        // DI01需要实时读取，其他从全局变量获取\n        value = global.get('currentDI01') || 0;\n    } else {\n        value = global.get(point) || 0;\n    }\n    \n    diStatus[point] = value;\n    node.log('DI状态 ' + point + ': ' + value);\n}\n\n// 存储到全局变量\ndiStatus.timestamp = Date.now();\nglobal.set('diStatus', diStatus);\n\nmsg.statusData = diStatus;\nmsg.statusType = 'DI';\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 2460, "y": 720, "wires": [["prepare_server_data_function"]]}, {"id": "get_do_status_function", "type": "function", "z": "d3caf6daf68cc456", "name": "获取DO状态", "func": "// 获取DO状态并存储\nvar doStatus = {};\nvar points = msg.points || [];\n\nnode.log('=== 采集DO状态 ===');\n\n// 从flow context获取DO运行状态\ndoStatus.DO21 = flow.get('waterPump1Running') ? 1 : 0;  // 水泵1\ndoStatus.DO22 = flow.get('waterPump2Running') ? 1 : 0;  // 水泵2\ndoStatus.DO23 = flow.get('airPump1Running') ? 1 : 0;    // 气泵1\ndoStatus.DO24 = flow.get('airPump2Running') ? 1 : 0;    // 气泵2\ndoStatus.DO01 = global.get('faultLightStatus') || 0;    // 故障灯\n\nfor (var point in doStatus) {\n    node.log('DO状态 ' + point + ': ' + doStatus[point]);\n}\n\n// 存储到全局变量\ndoStatus.timestamp = Date.now();\nglobal.set('doStatus', doStatus);\n\nmsg.statusData = doStatus;\nmsg.statusType = 'DO';\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 2460, "y": 760, "wires": [["prepare_server_data_function"]]}, {"id": "prepare_server_data_function", "type": "function", "z": "d3caf6daf68cc456", "name": "准备服务器数据", "func": "// 收集所有数据并准备发送到服务器\nvar statusType = msg.statusType;\n\n// 只有在DO状态采集完成后才发送数据\nif (statusType !== 'DO') {\n    return null;\n}\n\nnode.log('=== 准备服务器数据 ===');\n\n// 从全局变量获取所有采集的数据\nvar dianliucaiji2 = global.get('dianliucaiji2') || {};\nvar wenshidu = global.get('wenshidu') || {};\nvar diannengbiao = global.get('diannengbiao') || {};\nvar diStatus = global.get('diStatus') || {};\nvar doStatus = global.get('doStatus') || {};\nvar sn = (global.get('sn') || '').toString();\nvar imei = (global.get('imei') || '').toString();\n\n// 按照edge_report.json模版格式构建数据\nvar reportData = {\n    timestamp: new Date().toISOString(),\n    dianliucaiji2: {\n        curr2_ch1: dianliucaiji2.curr2_ch1 || 0,\n        curr2_ch2: dianliucaiji2.curr2_ch2 || 0,\n        curr2_ch3: dianliucaiji2.curr2_ch3 || 0,\n        curr2_ch4: dianliucaiji2.curr2_ch4 || 0\n    },\n    wenshidu: {\n        humidity: wenshidu.humidity || 0,\n        temperature: wenshidu.temperature || 0\n    },\n    diannengbiao: {\n        voltages: {\n            Ua: diannengbiao.Voltage_Ua || 0,\n            Ub: diannengbiao.Voltage_Ub || 0,\n            Uc: diannengbiao.Voltage_Uc || 0\n        },\n        currents: {\n            Ia: diannengbiao.Current_Ia || 0,\n            Ib: diannengbiao.Current_Ib || 0,\n            Ic: diannengbiao.Current_Ic || 0\n        },\n        active_power: {\n            total: diannengbiao.ActivePower_Pt || 0,\n            phaseA: diannengbiao.ActivePower_Pa || 0,\n            phaseB: diannengbiao.ActivePower_Pb || 0,\n            phaseC: diannengbiao.ActivePower_Pc || 0\n        },\n        active_energy: diannengbiao.ImpEp || 0\n    },\n    float_switches: {\n        float1: diStatus.DI01 || 0\n    },\n    water_pump1: {\n        auto_status: diStatus.DI15 || 0\n    },\n    water_pump2: {\n        auto_status: diStatus.DI16 || 0  \n    },\n    air_pump1: {\n        auto_status: diStatus.DI17 || 0\n    },\n    air_pump2: {\n        auto_status: diStatus.DI18 || 0\n    },\n    device_info: {\n        sn: sn,\n        imei: imei,\n        timestamp: Date.now()\n    },\n    DO21_status: doStatus.DO21 || 0,\n    DO22_status: doStatus.DO22 || 0,\n    DO23_status: doStatus.DO23 || 0,\n    DO24_status: doStatus.DO24 || 0\n};\n\nnode.log('数据报告已准备完成');\nnode.log('电流数据: ' + JSON.stringify(reportData.dianliucaiji2));\nnode.log('温湿度数据: ' + JSON.stringify(reportData.wenshidu));\nnode.log('电能表数据: ' + JSON.stringify(reportData.diannengbiao));\nnode.log('三相电压数据: A相=' + (diannengbiao.Voltage_Ua||0) + 'V, B相=' + (diannengbiao.Voltage_Ub||0) + 'V, C相=' + (diannengbiao.Voltage_Uc||0) + 'V');\nnode.log('三相电流数据: A相=' + (diannengbiao.Current_Ia||0) + 'A, B相=' + (diannengbiao.Current_Ib||0) + 'A, C相=' + (diannengbiao.Current_Ic||0) + 'A');\nnode.log('有功功率数据: 合相=' + (diannengbiao.ActivePower_Pt||0) + 'W, A相=' + (diannengbiao.ActivePower_Pa||0) + 'W, B相=' + (diannengbiao.ActivePower_Pb||0) + 'W, C相=' + (diannengbiao.ActivePower_Pc||0) + 'W');\nnode.log('DI状态: 浮球=' + reportData.float_switches.float1 + ', 自动档=' + diStatus.DI15 + ',' + diStatus.DI16 + ',' + diStatus.DI17 + ',' + diStatus.DI18);\nnode.log('DO状态: ' + JSON.stringify({DO21:doStatus.DO21, DO22:doStatus.DO22, DO23:doStatus.DO23, DO24:doStatus.DO24}));\nnode.log('设备标识: SN=' + sn + ', IMEI=' + imei);\n\nmsg.payload = reportData;\nmsg.topic = '/UploadTopic';\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 2680, "y": 740, "wires": [["send_to_server_function"]]}, {"id": "send_to_server_function", "type": "function", "z": "d3caf6daf68cc456", "name": "发送到服务器", "func": "node.log('=== 发送数据到TCP服务器 ===');\n\n// 配置TCP服务器信息\nvar serverHost = '**************';\nvar serverPort = 8889;\n\nvar payload = msg.payload;\nvar dataSize = JSON.stringify(payload).length;\n\nnode.log('TCP服务器地址: ' + serverHost + ':' + serverPort);\nnode.log('发送主题: ' + msg.topic);\nnode.log('数据大小: ' + dataSize + ' 字节');\nnode.log('发送时间: ' + new Date().toLocaleString());\n\n// 准备TCP发送的JSON数据\n// 添加设备标识信息（来自global.sn）\nvar sn = (global.get('sn') || '').toString();\nvar tcpData = {\n    sn: sn,\n    timestamp: Date.now(),\n    topic: msg.topic,\n    data: payload\n};\n\n// 转换为JSON字符串并添加换行符（TCP协议通常需要分隔符）\nvar jsonString = JSON.stringify(tcpData) + '\\n';\n\n// 设置TCP发送参数\nmsg.payload = jsonString;\nmsg.host = serverHost;\nmsg.port = serverPort;\n\n// 记录发送准备状态\nmsg.sendStatus = 'prepared';\nmsg.sendTime = new Date();\nmsg.dataSize = jsonString.length;\nmsg.serverInfo = {\n    host: serverHost,\n    port: serverPort,\n    protocol: 'tcp'\n};\n\nnode.log('TCP数据已准备完成，准备发送到: ' + serverHost + ':' + serverPort);\nnode.log('发送数据预览: ' + jsonString.substring(0, 200) + '...');\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 2880, "y": 740, "wires": [["tcp_out_node"]]}, {"id": "tcp_out_node", "type": "tcp out", "z": "d3caf6daf68cc456", "name": "TCP发送到服务器", "host": "**************", "port": "8889", "beserver": "client", "base64": false, "end": false, "tls": "", "x": 3100, "y": 740, "wires": []}, {"id": "tcp_connection_status", "type": "function", "z": "d3caf6daf68cc456", "name": "TCP连接状态监控", "func": "// 监控TCP连接状态和发送结果\nvar serverHost = '**************';\nvar serverPort = 8889;\n\nnode.log('=== TCP连接状态监控 ===');\n\nif (msg.payload) {\n    if (msg.payload.toString().includes('connected')) {\n        node.log('✓ TCP连接已建立到: ' + serverHost + ':' + serverPort);\n        msg.connectionStatus = 'connected';\n        msg.description = 'TCP连接成功';\n    } else if (msg.payload.toString().includes('closed')) {\n        node.log('✗ TCP连接已关闭');\n        msg.connectionStatus = 'disconnected';\n        msg.description = 'TCP连接关闭';\n    } else if (msg.payload.toString().includes('error')) {\n        node.log('✗ TCP连接出现错误: ' + msg.payload);\n        msg.connectionStatus = 'error';\n        msg.description = 'TCP连接错误: ' + msg.payload;\n    } else {\n        node.log('TCP状态更新: ' + msg.payload);\n        msg.connectionStatus = 'unknown';\n        msg.description = 'TCP状态: ' + msg.payload;\n    }\n} else {\n    node.log('TCP连接状态检查完成');\n    msg.connectionStatus = 'monitoring';\n    msg.description = 'TCP连接监控中';\n}\n\nmsg.monitorTime = new Date();\nmsg.serverEndpoint = serverHost + ':' + serverPort;\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 3100, "y": 800, "wires": [["server_send_debug"]]}, {"id": "server_send_debug", "type": "debug", "z": "d3caf6daf68cc456", "name": "TCP发送状态", "active": true, "tosidebar": true, "console": true, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 3320, "y": 800, "wires": []}, {"id": "data_collection_debug", "type": "debug", "z": "d3caf6daf68cc456", "name": "数据采集调试", "active": true, "tosidebar": true, "console": true, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 780, "y": 800, "wires": []}, {"id": "update_di01_inject", "type": "inject", "z": "d3caf6daf68cc456", "name": "更新DI01状态", "props": [{"p": "payload"}], "repeat": "1", "crontab": "", "once": true, "onceDelay": 0.5, "topic": "", "payload": "", "payloadType": "date", "x": 180, "y": 640, "wires": [["get_di01_for_data_function"]]}, {"id": "get_di01_for_data_function", "type": "function", "z": "d3caf6daf68cc456", "name": "触发DI01读取", "func": "// 触发DI01状态读取用于数据上报\nmsg.payload = '';\nmsg.purpose = 'data_collection';\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 380, "y": 640, "wires": [["get_di01_data_node"]]}, {"id": "get_di01_data_node", "type": "edge get node", "z": "d3caf6daf68cc456", "name": "读取DI01数据上报用", "nodeid": "DI01", "datatype": "INT", "x": 600, "y": 640, "wires": [["store_di01_for_data_function"]]}, {"id": "store_di01_for_data_function", "type": "function", "z": "d3caf6daf68cc456", "name": "存储DI01用于数据上报", "func": "// 存储DI01状态供数据上报使用\nvar value = parseInt(msg.payload) || 0;\nglobal.set('currentDI01', value);\n\nnode.log('DI01状态更新(数据上报用): ' + value);\n\nmsg.di01Value = value;\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 840, "y": 640, "wires": [["data_collection_debug"]]}]